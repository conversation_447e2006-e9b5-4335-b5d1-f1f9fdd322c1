// Flattened type structure (after optimization)
export interface UserProfileFlat {
  // Basic info
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  dateOfBirth?: Date;
  
  // Address (flattened)
  addressStreet: string;
  addressCity: string;
  addressState: string;
  addressZipCode: string;
  addressCountry: string;
  addressLatitude?: number;
  addressLongitude?: number;
  addressAccuracy?: number;
  addressTimestamp?: Date;
  
  // Avatar (flattened)
  avatarUrl?: string;
  avatarThumbnailUrl?: string;
  avatarSize?: number;
  avatarFormat?: string;
  avatarWidth?: number;
  avatarHeight?: number;
  avatarUploadedAt?: Date;
  avatarUploadedBy?: string;
  
  // Preferences (flattened)
  theme: 'light' | 'dark' | 'auto';
  language: string;
  timezone: string;
  
  // Notifications (flattened)
  emailMarketing: boolean;
  emailUpdates: boolean;
  emailSecurity: boolean;
  emailFrequency: 'immediate' | 'daily' | 'weekly' | 'never';
  pushEnabled: boolean;
  pushCategories: string[];
  pushQuietStart?: string;
  pushQuietEnd?: string;
  pushQuietTimezone?: string;
  smsEnabled: boolean;
  smsEmergencyOnly: boolean;
  
  // Privacy (flattened)
  profileVisibility: 'public' | 'friends' | 'private';
  showOnlineStatus: boolean;
  allowDirectMessages: boolean;
  analyticsSharing: boolean;
  marketingSharing: boolean;
  thirdPartySharing: boolean;
  
  // Metadata (flattened)
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
  loginCount: number;
  isVerified: boolean;
  verificationMethod?: 'email' | 'phone' | 'document';
  accountStatus: 'active' | 'suspended' | 'pending' | 'deleted';
  
  // Subscription (flattened)
  subscriptionPlan?: string;
  subscriptionStatus?: 'active' | 'cancelled' | 'expired' | 'trial';
  subscriptionStartDate?: Date;
  subscriptionEndDate?: Date;
  subscriptionAutoRenew?: boolean;
  paymentType?: 'card' | 'paypal' | 'bank';
  paymentLast4?: string;
  paymentExpiryDate?: string;
}

export type CreateUserFlatRequest = Omit<UserProfileFlat, 'id' | 'createdAt' | 'updatedAt' | 'loginCount' | 'lastLoginAt'> & {
  password: string;
  confirmPassword: string;
};

export type UpdateUserFlatRequest = Partial<Omit<UserProfileFlat, 'id' | 'createdAt'>>;

export type UserFlatSummary = Pick<UserProfileFlat, 'id' | 'firstName' | 'lastName' | 'email' | 'accountStatus'>;

export type UserFlatListResponse = {
  users: UserFlatSummary[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  filters: {
    status?: string;
    role?: string;
    createdAfter?: Date;
    createdBefore?: Date;
  };
};
